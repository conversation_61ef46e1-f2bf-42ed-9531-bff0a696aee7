// y系列公共样式
@use './mixin.scss' as *;

// 页面
.#{$namespace}-page {
  @include full;
}

// 通用bar
.#{$namespace}-bar {
  @include bar;

  &.no-padding {
    padding: 0;
  }
}

// 头部
.#{$namespace}-header {
  @include bar(12px, 24px, flex-start, center);
  border-bottom: 1px solid $borderColor;

  .#{$namespace}-title {
    flex: 1;
    white-space: nowrap;
    line-height: 34px;
  }
}

// 底部
.#{$namespace}-footer {
  @include bar(12px, 24px, flex-end, center);
  border-top: 1px solid $borderColor;

  .el-button {
    padding: 12px 40px;
  }
}

// 普通容器，可以配合no-padding类名
.#{$namespace}-container {
  @include container;
  // overflow: hidden;

  &.no-padding {
    padding: 0;
  }
}

// 限制大小overflow:auto的容器，可以配合no-padding类名
.#{$namespace}-container--tight {
  @include container;
  overflow: auto;

  &.no-padding {
    padding: 0;
  }
}

// 标题
.#{$namespace}-title {
  @include title;
}

// 前面有竖线的标题
.#{$namespace}-title--secondary {
  @include title;
  position: relative;
  margin-left: 12px;
  font-weight: 400;
  color: $txtColor;

  &:before {
    content: '';
    position: absolute;
    left: -12px;
    width: 4px;
    height: 100%;
    background-color: $themeColor;
  }
}

// 卡片布局
.#{$namespace}-card-wrapper {
  display: grid;
  justify-content: space-around;
  gap: 16px;
  flex-wrap: wrap;
  overflow: unset;
  overflow-y: auto;
}

// 数据项
.#{$namespace}-item {
  font-size: 14px;

  .label {
    color: $txtColor-light;
  }

  .value {
    color: $txtColor;
    margin-left: 8px;
  }
}

// 前缀
.#{$namespace}-prefixer {
  font-size: 14px;
  font-weight: 400;
  color: $txtColor-light;
}